# 词库系统开发指南

> **文档版本**: v1.0  
> **创建时间**: 2025年8月2日  
> **最后更新**: 2025年8月2日 16:42:27 CST  
> **项目**: 矩阵内填词与查重系统  

## 📋 目录

- [1. 项目概述](#1-项目概述)
- [2. 核心数据结构设计](#2-核心数据结构设计)
- [3. 前端架构设计](#3-前端架构设计)
- [4. 后端架构设计](#4-后端架构设计)
- [5. 前后端通讯交互](#5-前后端通讯交互)
- [6. 数据持久化方案](#6-数据持久化方案)
- [7. 系统架构图](#7-系统架构图)
- [8. 业务流程时序图](#8-业务流程时序图)
- [9. 性能优化方案](#9-性能优化方案)
- [10. 实施计划](#10-实施计划)

## 1. 项目概述

### 1.1 业务需求

基于33x33矩阵网格系统，实现词库管理和填词功能，包括：

- 词库按颜色(8种)和等级(4级)分类管理
- 双击激活填词模式，键盘导航选择词语
- 实时重复检测和视觉高亮
- 版本管理和数据同步
- 中文字符验证和错误处理

### 1.2 技术栈

- **前端**: Next.js 15.1.0, React 18.3.1, TypeScript 5.8.3, Zustand 5.0.6
- **后端**: FastAPI 0.116.1, Python 3.11+, Pydantic
- **数据库**: PostgreSQL + Redis缓存
- **构建工具**: Turbo 2.3.0 + pnpm 9.15.0

### 1.3 核心功能

1. **词库管理**: 添加、编辑、删除、分类管理词语
2. **填词功能**: 双击激活、键盘导航、预览确认
3. **重复检测**: 实时检测、视觉高亮、冲突解决
4. **版本管理**: 保存版本、加载历史、版本比较
5. **数据同步**: 本地缓存、服务器同步、离线支持

## 2. 核心数据结构设计

### 2.1 词语条目数据结构

```typescript
interface WordEntry {
  id: string;                    // 唯一标识符 (UUID)
  text: string;                  // 中文词语文本
  category: {
    color: BasicColorType;       // 所属颜色类别 (红、青、黄、紫、橙、绿、蓝、粉)
    level: ColorLevel;           // 所属等级 (1-4)
  };
  metadata: {
    createdAt: Date;            // 创建时间
    updatedAt: Date;            // 更新时间
    usageCount: number;         // 使用次数统计
    lastUsed?: Date;            // 最后使用时间
  };
  validation: {
    isValid: boolean;           // 是否为有效中文词语
    validationErrors?: string[]; // 验证错误信息
  };
}
```

### 2.2 词库数据结构

```typescript
interface WordLibraryData {
  categories: Record<BasicColorType, Record<ColorLevel, WordEntry[]>>;
  metadata: {
    totalWords: number;
    lastUpdated: Date;
    version: string;
  };
  statistics: {
    wordsByColor: Record<BasicColorType, number>;
    wordsByLevel: Record<ColorLevel, number>;
    duplicateWords: string[];
  };
}
```

### 2.3 单元格词语绑定

```typescript
interface CellWordBinding {
  cellKey: string;              // "x,y" 格式的单元格键
  wordId: string;               // 绑定的词语ID
  bindingTime: Date;            // 绑定时间
  isTemporary?: boolean;        // 是否为临时绑定(预览状态)
}

// 绑定关系映射
type CellWordBindings = Map<string, CellWordBinding>;
```

### 2.4 版本管理和导航状态

```typescript
interface WordVersion {
  id: string;                   // 版本唯一标识符
  name: string;                 // 版本名称
  description?: string;         // 版本描述
  createdAt: Date;             // 创建时间
  bindings: CellWordBinding[];  // 该版本的所有绑定关系
  metadata: {
    totalBindings: number;      // 绑定总数
    coverageRate: number;       // 覆盖率
    wordCount: number;          // 使用的词语总数
    duplicateCount: number;     // 重复词语数量
  };
  checksum: string;            // 数据校验和
}

interface NavigationState {
  isActive: boolean;            // 是否处于选择模式
  targetCell: { x: number; y: number } | null;
  availableWords: WordEntry[];  // 当前可选词语列表
  selectedIndex: number;        // 当前选择索引
  lastSelectedIndex: Map<string, number>; // 每个分类的上次选择位置
  previewMode: boolean;         // 是否显示预览
  filterCriteria: {            // 过滤条件
    color?: BasicColorType;
    level?: ColorLevel;
    searchText?: string;
  };
}
```

### 2.5 重复检测数据结构

```typescript
interface DuplicateDetection {
  duplicateGroups: Map<string, DuplicateGroup>;  // 按词语文本分组
  duplicateWords: Set<string>;                   // 重复词语集合
  statistics: {
    totalDuplicates: number;                     // 重复词语总数
    duplicateRate: number;                       // 重复率
    maxUsageCount: number;                       // 最大使用次数
  };
  lastDetectionTime: Date;                       // 最后检测时间
}

interface DuplicateGroup {
  wordText: string;                              // 词语文本
  positions: Array<{ x: number; y: number }>;   // 使用位置列表
  usageCount: number;                            // 使用次数
  wordIds: string[];                             // 相关词语ID列表
}
```

## 3. 前端架构设计

### 3.1 WordLibraryStore 设计

基于现有的Zustand架构，创建专门的词库状态管理：

```typescript
interface WordLibraryStore {
  // === 核心数据 ===
  wordLibrary: WordLibraryData;
  cellWordBindings: CellWordBindings;
  wordVersions: WordVersion[];
  currentVersionId: string | null;
  
  // === 导航和交互状态 ===
  navigationState: NavigationState;
  duplicateDetection: DuplicateDetection;
  
  // === 词库管理操作 ===
  addWord: (word: Omit<WordEntry, 'id' | 'metadata'>) => Promise<void>;
  updateWord: (wordId: string, updates: Partial<WordEntry>) => Promise<void>;
  removeWord: (wordId: string) => Promise<void>;
  validateWord: (text: string) => boolean;
  
  // === 单元格绑定操作 ===
  bindWordToCell: (x: number, y: number, wordId: string) => Promise<void>;
  unbindWordFromCell: (x: number, y: number) => Promise<void>;
  previewWordInCell: (x: number, y: number, wordId: string) => void;
  clearPreview: () => void;
  
  // === 导航操作 ===
  activateWordSelection: (x: number, y: number) => void;
  navigateWords: (direction: 'left' | 'right' | 'up' | 'down') => void;
  confirmSelection: () => Promise<void>;
  cancelSelection: () => void;
  
  // === 版本管理操作 ===
  saveWordVersion: (name: string, description?: string) => Promise<void>;
  loadWordVersion: (versionId: string) => Promise<void>;
  deleteWordVersion: (versionId: string) => Promise<void>;
  
  // === 数据同步操作 ===
  syncWithServer: () => Promise<void>;
  exportWordLibrary: () => string;
  importWordLibrary: (data: string) => Promise<void>;
}
```

### 3.2 核心组件设计

```typescript
// 词库管理面板
interface WordLibraryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCategory?: { color: BasicColorType; level: ColorLevel };
}

// 词语选择覆盖层
interface WordSelectionOverlayProps {
  targetCell: { x: number; y: number };
  availableWords: WordEntry[];
  selectedIndex: number;
  onSelect: (wordId: string) => void;
  onCancel: () => void;
}

// 版本管理面板
interface WordVersionPanelProps {
  versions: WordVersion[];
  currentVersionId: string | null;
  onSaveVersion: (name: string, description?: string) => void;
  onLoadVersion: (versionId: string) => void;
  onDeleteVersion: (versionId: string) => void;
}

// 重复高亮组件
interface DuplicateHighlightProps {
  duplicateInfo: DuplicateDetection;
  highlightEnabled: boolean;
  onToggleHighlight: (enabled: boolean) => void;
}
```

### 3.3 键盘导航服务

```typescript
class KeyboardNavigationService {
  private store: WordLibraryStore;
  
  constructor(store: WordLibraryStore) {
    this.store = store;
    this.setupKeyboardListeners();
  }
  
  private setupKeyboardListeners(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }
  
  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.store.navigationState.isActive) return;
    
    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowRight':
        this.store.navigateWords(event.key === 'ArrowLeft' ? 'left' : 'right');
        break;
      case 'ArrowUp':
        this.store.previewWordInCell(/* ... */);
        break;
      case 'ArrowDown':
        this.store.clearPreview();
        break;
      case 'Enter':
        this.store.confirmSelection();
        break;
      case 'Escape':
        this.store.cancelSelection();
        break;
    }
  }
}
```

## 4. 后端架构设计

### 4.1 RESTful API端点

```python
# 词库管理API
GET    /api/v1/word-library/                    # 获取词库
POST   /api/v1/word-library/words/              # 添加词语
PUT    /api/v1/word-library/words/{word_id}     # 更新词语
DELETE /api/v1/word-library/words/{word_id}     # 删除词语
POST   /api/v1/word-library/words/batch         # 批量操作
POST   /api/v1/word-library/words/validate      # 词语验证

# 单元格绑定API
GET    /api/v1/word-bindings/                   # 获取所有绑定
POST   /api/v1/word-bindings/                   # 创建绑定
PUT    /api/v1/word-bindings/{binding_id}       # 更新绑定
DELETE /api/v1/word-bindings/{binding_id}       # 删除绑定
POST   /api/v1/word-bindings/batch              # 批量绑定操作

# 版本管理API
GET    /api/v1/word-versions/                   # 获取所有版本
POST   /api/v1/word-versions/                   # 创建新版本
POST   /api/v1/word-versions/{version_id}/load  # 加载版本
PUT    /api/v1/word-versions/{version_id}       # 更新版本信息
DELETE /api/v1/word-versions/{version_id}       # 删除版本

# 重复检测API
GET    /api/v1/duplicates/                      # 获取重复检测结果
POST   /api/v1/duplicates/analyze               # 执行重复分析

# 数据同步API
GET    /api/v1/sync/status                      # 获取同步状态
GET    /api/v1/sync/changes                     # 获取增量变更
POST   /api/v1/sync/batch                       # 批量同步操作
```

### 4.2 Pydantic数据模型

```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BasicColorType(str, Enum):
    RED = "red"
    CYAN = "cyan"
    YELLOW = "yellow"
    PURPLE = "purple"
    ORANGE = "orange"
    GREEN = "green"
    BLUE = "blue"
    PINK = "pink"

class ColorLevel(int, Enum):
    LEVEL_1 = 1
    LEVEL_2 = 2
    LEVEL_3 = 3
    LEVEL_4 = 4

class WordCategory(BaseModel):
    color: BasicColorType
    level: ColorLevel

class WordMetadata(BaseModel):
    created_at: datetime
    updated_at: datetime
    usage_count: int = 0
    last_used: Optional[datetime] = None

class WordValidation(BaseModel):
    is_valid: bool = True
    validation_errors: Optional[List[str]] = None

class WordEntry(BaseModel):
    id: str
    text: str
    category: WordCategory
    metadata: WordMetadata
    validation: WordValidation
    
    @validator('text')
    def validate_chinese_text(cls, v):
        import re
        if not re.match(r'^[\u4e00-\u9fff]+$', v):
            raise ValueError('词语必须包含有效的中文字符')
        return v

class CellWordBinding(BaseModel):
    id: str
    cell_key: str  # "x,y" 格式
    word_id: str
    binding_time: datetime
    is_temporary: bool = False

class WordVersionMetadata(BaseModel):
    total_bindings: int
    coverage_rate: float
    word_count: int
    duplicate_count: int

class WordVersion(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    created_at: datetime
    bindings: List[CellWordBinding]
    metadata: WordVersionMetadata
    checksum: str

class DuplicateGroup(BaseModel):
    word_text: str
    positions: List[Dict[str, int]]  # [{"x": 1, "y": 2}, ...]
    usage_count: int
    word_ids: List[str]

class DuplicateDetectionResult(BaseModel):
    duplicate_groups: Dict[str, DuplicateGroup]
    duplicate_words: List[str]
    statistics: Dict[str, Any]
    last_detection_time: datetime
```

### 4.3 数据库模型设计

```python
# 数据库模型设计 (SQLModel)
from sqlmodel import SQLModel, Field, Index, UniqueConstraint
from datetime import datetime
from typing import Optional

class WordEntryDB(SQLModel, table=True):
    __tablename__ = "word_entries"

    id: str = Field(primary_key=True)
    text: str = Field(index=True)
    color: str = Field(index=True)
    level: int = Field(index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    usage_count: int = Field(default=0)
    last_used: Optional[datetime] = None
    is_valid: bool = Field(default=True)
    validation_errors: Optional[str] = None  # JSON string

class CellWordBindingDB(SQLModel, table=True):
    __tablename__ = "cell_word_bindings"

    id: str = Field(primary_key=True)
    cell_x: int = Field(index=True)
    cell_y: int = Field(index=True)
    word_id: str = Field(foreign_key="word_entries.id", index=True)
    binding_time: datetime = Field(default_factory=datetime.utcnow)
    is_temporary: bool = Field(default=False)

    # 复合索引
    __table_args__ = (
        Index('idx_cell_position', 'cell_x', 'cell_y'),
        UniqueConstraint('cell_x', 'cell_y', name='unique_cell_binding'),
    )

class WordVersionDB(SQLModel, table=True):
    __tablename__ = "word_versions"

    id: str = Field(primary_key=True)
    name: str = Field(index=True)
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    bindings_data: str = Field()  # JSON string of bindings
    metadata: str = Field()  # JSON string of metadata
    checksum: str = Field(index=True)
```

## 5. 前后端通讯交互

### 5.1 数据同步策略

```typescript
// 前端数据同步服务
class WordLibraryService {
  // 增量同步 - 只同步变更的数据
  async syncIncremental(): Promise<void> {
    const lastSyncTime = this.getLastSyncTime();
    const changes = await this.api.getChanges(lastSyncTime);
    await this.applyChanges(changes);
  }

  // 全量同步 - 同步所有数据
  async syncFull(): Promise<void> {
    const serverData = await this.api.getFullWordLibrary();
    await this.replaceLocalData(serverData);
  }

  // 冲突解决策略
  async resolveConflicts(conflicts: DataConflict[]): Promise<void> {
    // 服务器优先策略或用户选择界面
  }
}
```

### 5.2 实时通讯设计

```typescript
// WebSocket连接用于实时协作
class WordLibraryWebSocket {
  private ws: WebSocket;

  // 监听词库变更
  onWordLibraryChange(callback: (change: WordLibraryChange) => void): void;

  // 监听绑定变更
  onBindingChange(callback: (change: BindingChange) => void): void;

  // 广播用户操作
  broadcastUserAction(action: UserAction): void;
}
```

### 5.3 缓存策略

```typescript
// 多层缓存设计
interface CacheStrategy {
  // L1: 内存缓存 (Zustand Store)
  memoryCache: Map<string, any>;

  // L2: 浏览器缓存 (IndexedDB)
  browserCache: IDBDatabase;

  // L3: 服务器缓存 (Redis)
  serverCache: RedisClient;
}
```

### 5.4 后端缓存层

```python
# Redis缓存层
class WordLibraryCache:
    async def get_word_library(self) -> Optional[WordLibraryData]:
        cached = await self.redis.get("word_library:full")
        if cached:
            return WordLibraryData.parse_raw(cached)
        return None

    async def invalidate_cache(self, pattern: str = "word_library:*"):
        keys = await self.redis.keys(pattern)
        if keys:
            await self.redis.delete(*keys)
```

## 6. 数据持久化方案

### 6.1 前端持久化

```typescript
// 使用Zustand持久化中间件
const wordLibraryStore = create<WordLibraryStore>()(
  persist(
    (set, get) => ({
      // store implementation
    }),
    {
      name: 'word-library-store',
      version: 1,
      // 自定义序列化，处理Map和Set
      serialize: (state) => JSON.stringify({
        ...state,
        cellWordBindings: Array.from(state.cellWordBindings.entries()),
        duplicateDetection: {
          ...state.duplicateDetection,
          duplicateGroups: Array.from(state.duplicateDetection.duplicateGroups.entries()),
          duplicateWords: Array.from(state.duplicateDetection.duplicateWords),
        }
      }),
      deserialize: (str) => {
        const state = JSON.parse(str);
        return {
          ...state,
          cellWordBindings: new Map(state.cellWordBindings),
          duplicateDetection: {
            ...state.duplicateDetection,
            duplicateGroups: new Map(state.duplicateDetection.duplicateGroups),
            duplicateWords: new Set(state.duplicateDetection.duplicateWords),
          }
        };
      },
      // 部分持久化，排除临时状态
      partialize: (state) => ({
        wordLibrary: state.wordLibrary,
        cellWordBindings: state.cellWordBindings,
        wordVersions: state.wordVersions,
        currentVersionId: state.currentVersionId,
      }),
    }
  )
);
```

### 6.2 后端数据库查询优化

```python
# 数据库查询优化
class WordLibraryRepository:
    async def get_words_by_category(
        self,
        color: Optional[str] = None,
        level: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[WordEntry]:
        query = select(WordEntryDB)

        if color:
            query = query.where(WordEntryDB.color == color)
        if level:
            query = query.where(WordEntryDB.level == level)

        # 使用索引优化查询
        query = query.order_by(WordEntryDB.created_at.desc())
        query = query.limit(limit).offset(offset)

        result = await self.session.execute(query)
        return result.scalars().all()
```

## 7. 系统架构图

### 7.1 整体架构图

```mermaid
graph TB
    subgraph "前端层 Frontend Layer"
        subgraph "UI组件层 UI Components"
            A[GridCell组件] --> B[词语显示覆盖层]
            C[WordLibraryPanel] --> D[词库管理界面]
            E[WordSelectionOverlay] --> F[键盘导航界面]
            G[WordVersionPanel] --> H[版本管理界面]
            I[DuplicateHighlight] --> J[重复检测显示]
        end

        subgraph "状态管理层 State Management"
            K[WordLibraryStore] --> L[词库数据管理]
            K --> M[单元格绑定管理]
            K --> N[版本管理]
            K --> O[导航状态管理]
            P[MatrixStore] --> Q[网格状态集成]
        end

        subgraph "业务逻辑层 Business Logic"
            R[WordLibraryService] --> S[数据同步服务]
            T[KeyboardNavigationService] --> U[键盘导航逻辑]
            V[DuplicateDetectionService] --> W[重复检测算法]
            X[ValidationService] --> Y[中文字符验证]
        end

        subgraph "数据持久化层 Data Persistence"
            Z[LocalStorage] --> AA[本地数据缓存]
            AB[IndexedDB] --> AC[大数据量存储]
            AD[Zustand Persist] --> AE[状态持久化]
        end
    end

    subgraph "通讯层 Communication Layer"
        AF[HTTP Client] --> AG[RESTful API调用]
        AH[WebSocket Client] --> AI[实时数据同步]
        AJ[Cache Manager] --> AK[多层缓存管理]
    end

    subgraph "后端层 Backend Layer"
        subgraph "API层 API Layer"
            AL[FastAPI Router] --> AM[词库管理API]
            AL --> AN[绑定管理API]
            AL --> AO[版本管理API]
            AL --> AP[重复检测API]
        end

        subgraph "业务逻辑层 Business Logic"
            AQ[WordLibraryRepository] --> AR[词库数据操作]
            AS[BindingRepository] --> AT[绑定关系操作]
            AU[VersionRepository] --> AV[版本数据操作]
            AW[DuplicateAnalyzer] --> AX[服务端重复分析]
        end

        subgraph "数据层 Data Layer"
            AY[PostgreSQL] --> AZ[关系型数据存储]
            BA[Redis] --> BB[缓存和会话存储]
            BC[SQLModel] --> BD[ORM数据模型]
        end
    end

    %% 连接关系
    A --> K
    C --> K
    E --> T
    G --> K
    I --> V

    K --> R
    K --> AD

    R --> AF
    T --> K
    V --> K
    X --> K

    AF --> AL
    AH --> AL

    AM --> AQ
    AN --> AS
    AO --> AU
    AP --> AW

    AQ --> AY
    AS --> AY
    AU --> AY
    AW --> BA

    AY --> BC
    BA --> BC

    %% 样式
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef communication fill:#e8f5e8
    classDef database fill:#fff3e0

    class A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE frontend
    class AF,AG,AH,AI,AJ,AK communication
    class AL,AM,AN,AO,AP,AQ,AR,AS,AT,AU,AV,AW,AX backend
    class AY,AZ,BA,BB,BC,BD database
```

## 8. 业务流程时序图

### 8.1 完整填词流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant GridCell as GridCell组件
    participant WordStore as WordLibraryStore
    participant NavService as 键盘导航服务
    participant WordOverlay as 词语选择界面
    participant DupService as 重复检测服务
    participant API as 后端API
    participant DB as 数据库

    Note over User, DB: 填词流程开始

    %% 1. 激活填词模式
    User->>GridCell: 双击单元格(x,y)
    GridCell->>WordStore: activateWordSelection(x, y)
    WordStore->>WordStore: 检查单元格颜色和等级
    WordStore->>WordStore: 过滤匹配的词语列表
    WordStore->>NavService: 初始化导航状态
    NavService->>WordOverlay: 显示词语选择界面
    WordOverlay->>User: 显示可选词语列表

    Note over User, DB: 键盘导航选择

    %% 2. 键盘导航
    User->>NavService: 按左右键导航
    NavService->>WordStore: navigateWords('left'/'right')
    WordStore->>WordStore: 更新selectedIndex
    WordStore->>GridCell: 预览当前选中词语
    GridCell->>User: 显示预览效果

    %% 3. 预览模式
    User->>NavService: 按上键预览
    NavService->>WordStore: previewWordInCell(x, y, wordId)
    WordStore->>GridCell: 临时显示词语
    GridCell->>User: 显示词语预览

    User->>NavService: 按下键隐藏预览
    NavService->>WordStore: clearPreview()
    WordStore->>GridCell: 清除预览显示
    GridCell->>User: 隐藏词语预览

    Note over User, DB: 确认选择和数据同步

    %% 4. 确认选择
    User->>NavService: 按回车确认
    NavService->>WordStore: confirmSelection()
    WordStore->>WordStore: 创建绑定关系
    WordStore->>DupService: 检测重复词语
    DupService->>WordStore: 返回重复检测结果
    WordStore->>API: POST /api/v1/word-bindings/
    API->>DB: 保存绑定关系
    DB->>API: 返回保存结果
    API->>WordStore: 返回绑定确认
    WordStore->>GridCell: 永久显示词语
    WordStore->>WordOverlay: 关闭选择界面
    GridCell->>User: 显示最终绑定结果

    Note over User, DB: 重复检测和高亮

    %% 5. 重复检测
    WordStore->>DupService: detectDuplicates()
    DupService->>WordStore: 返回重复词语信息
    WordStore->>GridCell: 高亮重复词语单元格
    GridCell->>User: 显示重复词语视觉指示

    Note over User, DB: 流程结束
```

### 8.2 词库管理时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant LibraryPanel as 词库管理面板
    participant WordStore as WordLibraryStore
    participant Validator as 验证服务
    participant API as 后端API
    participant DB as 数据库
    participant Cache as Redis缓存

    Note over User, Cache: 词库管理流程

    %% 1. 打开词库面板
    User->>LibraryPanel: 打开词库管理面板
    LibraryPanel->>WordStore: 获取词库数据
    WordStore->>API: GET /api/v1/word-library/
    API->>Cache: 检查缓存

    alt 缓存命中
        Cache->>API: 返回缓存数据
    else 缓存未命中
        API->>DB: 查询词库数据
        DB->>API: 返回词库数据
        API->>Cache: 更新缓存
    end

    API->>WordStore: 返回词库数据
    WordStore->>LibraryPanel: 更新界面数据
    LibraryPanel->>User: 显示分类词库

    Note over User, Cache: 添加新词语

    %% 2. 添加词语
    User->>LibraryPanel: 输入新词语
    LibraryPanel->>Validator: 验证中文字符
    Validator->>LibraryPanel: 返回验证结果

    alt 验证通过
        LibraryPanel->>WordStore: addWord(wordData)
        WordStore->>WordStore: 检查重复词语

        alt 无重复
            WordStore->>API: POST /api/v1/word-library/words/
            API->>DB: 保存新词语
            DB->>API: 返回保存结果
            API->>Cache: 清除相关缓存
            API->>WordStore: 返回新词语数据
            WordStore->>LibraryPanel: 更新词库显示
            LibraryPanel->>User: 显示添加成功
        else 存在重复
            WordStore->>LibraryPanel: 返回重复错误
            LibraryPanel->>User: 显示重复警告
        end
    else 验证失败
        LibraryPanel->>User: 显示验证错误
    end
```

### 8.3 版本管理时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant VersionPanel as 版本管理面板
    participant WordStore as WordLibraryStore
    participant API as 后端API
    participant DB as 数据库
    participant Cache as Redis缓存

    Note over User, Cache: 版本管理流程

    %% 1. 保存当前版本
    User->>VersionPanel: 点击"保存版本"
    VersionPanel->>User: 显示版本信息输入框
    User->>VersionPanel: 输入版本名称和描述
    VersionPanel->>WordStore: saveWordVersion(name, description)

    WordStore->>WordStore: 收集当前绑定状态
    WordStore->>WordStore: 计算版本元数据
    Note right of WordStore: 计算绑定总数、覆盖率、<br/>词语数量、重复数量等
    WordStore->>WordStore: 生成数据校验和

    WordStore->>API: POST /api/v1/word-versions/
    API->>DB: 保存版本数据
    DB->>API: 返回版本ID
    API->>Cache: 缓存版本信息
    API->>WordStore: 返回保存结果
    WordStore->>VersionPanel: 更新版本列表
    VersionPanel->>User: 显示保存成功

    Note over User, Cache: 加载历史版本

    %% 2. 加载版本
    User->>VersionPanel: 选择历史版本
    VersionPanel->>WordStore: loadWordVersion(versionId)
    WordStore->>API: POST /api/v1/word-versions/{versionId}/load

    API->>Cache: 检查版本缓存
    alt 缓存命中
        Cache->>API: 返回版本数据
    else 缓存未命中
        API->>DB: 查询版本数据
        DB->>API: 返回版本数据
        API->>Cache: 更新缓存
    end

    API->>WordStore: 返回版本数据
    WordStore->>WordStore: 验证数据完整性
    Note right of WordStore: 使用校验和验证数据完整性

    alt 数据完整
        WordStore->>WordStore: 清除当前绑定
        WordStore->>WordStore: 应用版本绑定
        WordStore->>WordStore: 更新当前版本ID
        WordStore->>VersionPanel: 通知加载完成
        VersionPanel->>User: 显示版本加载成功

        %% 通知网格更新
        WordStore->>GridCell: 更新所有单元格显示
        GridCell->>User: 显示新版本的词语绑定
    else 数据损坏
        WordStore->>VersionPanel: 返回错误信息
        VersionPanel->>User: 显示数据损坏警告
    end
```

### 8.4 重复检测时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant GridCell as GridCell组件
    participant WordStore as WordLibraryStore
    participant DupService as 重复检测服务
    participant HighlightService as 高亮显示服务
    participant API as 后端API
    participant DB as 数据库

    Note over User, DB: 重复检测流程

    %% 1. 触发重复检测
    User->>GridCell: 绑定新词语到单元格
    GridCell->>WordStore: bindWordToCell(x, y, wordId)
    WordStore->>WordStore: 更新绑定关系
    WordStore->>DupService: detectDuplicates()

    %% 2. 本地重复检测
    DupService->>DupService: 扫描所有绑定关系
    DupService->>DupService: 按词语文本分组
    DupService->>DupService: 识别重复词语
    Note right of DupService: 统计每个词语的使用次数<br/>和位置信息

    DupService->>DupService: 计算重复统计
    Note right of DupService: 计算重复率、最大使用次数、<br/>重复词语总数等指标

    DupService->>WordStore: 返回重复检测结果
    WordStore->>WordStore: 更新重复检测状态

    %% 3. 服务端同步检测
    WordStore->>API: POST /api/v1/duplicates/analyze
    API->>DB: 查询所有绑定关系
    DB->>API: 返回绑定数据
    API->>API: 执行服务端重复分析
    Note right of API: 服务端算法可能包含<br/>更复杂的语义分析
    API->>WordStore: 返回服务端检测结果

    %% 4. 合并检测结果
    WordStore->>DupService: 合并本地和服务端结果
    DupService->>DupService: 对比和合并结果
    DupService->>WordStore: 返回最终检测结果

    %% 5. 高亮显示重复词语
    WordStore->>HighlightService: highlightDuplicateWords(duplicateInfo)
    HighlightService->>HighlightService: 计算需要高亮的单元格

    loop 遍历重复词语组
        HighlightService->>GridCell: 添加重复高亮样式
        GridCell->>GridCell: 应用重复词语样式
        Note right of GridCell: 添加边框、背景色、<br/>或其他视觉指示
    end

    HighlightService->>WordStore: 高亮应用完成
    WordStore->>User: 显示重复检测结果
```

### 8.5 数据同步时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant WordStore as WordLibraryStore
    participant SyncService as 同步服务
    participant LocalStorage as 本地存储
    participant API as 后端API
    participant DB as 数据库
    participant WebSocket as WebSocket服务

    Note over User, WebSocket: 数据同步流程

    %% 1. 应用启动时的数据同步
    User->>WordStore: 应用启动
    WordStore->>LocalStorage: 加载本地数据
    LocalStorage->>WordStore: 返回本地缓存数据
    WordStore->>SyncService: 检查同步状态
    SyncService->>SyncService: 获取最后同步时间

    SyncService->>API: GET /api/v1/sync/status
    API->>DB: 查询服务端最后更新时间
    DB->>API: 返回更新时间戳
    API->>SyncService: 返回同步状态

    alt 需要同步
        SyncService->>SyncService: 启动增量同步
        SyncService->>API: GET /api/v1/sync/changes?since={timestamp}
        API->>DB: 查询增量变更
        DB->>API: 返回变更数据
        API->>SyncService: 返回增量数据

        SyncService->>WordStore: 应用增量变更
        WordStore->>LocalStorage: 更新本地缓存
        SyncService->>SyncService: 更新同步时间戳
        SyncService->>WordStore: 同步完成
    else 数据已是最新
        SyncService->>WordStore: 无需同步
    end

    WordStore->>User: 应用就绪
```

## 9. 性能优化方案

### 9.1 前端性能优化

```typescript
// 虚拟化词库显示
const VirtualizedWordList = React.memo(({ words, onSelect }) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 50 });

  // 只渲染可见范围内的词语
  const visibleWords = useMemo(() =>
    words.slice(visibleRange.start, visibleRange.end),
    [words, visibleRange]
  );

  return <FixedSizeList {...props} />;
});

// 防抖键盘导航
const useKeyboardNavigation = () => {
  const debouncedNavigate = useMemo(
    () => debounce((direction: string) => {
      // 导航逻辑
    }, 100),
    []
  );

  return { navigate: debouncedNavigate };
};

// 智能缓存计算
const useDuplicateDetection = () => {
  return useMemo(() => {
    // 只在绑定关系变化时重新计算
    return detectDuplicates(cellWordBindings);
  }, [cellWordBindings]);
};
```

### 9.2 后端性能优化

```python
# 数据库查询优化
class WordLibraryRepository:
    async def get_words_by_category_optimized(
        self,
        color: Optional[str] = None,
        level: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[WordEntry]:
        # 使用预编译查询和索引优化
        query = select(WordEntryDB).options(
            selectinload(WordEntryDB.bindings)  # 预加载关联数据
        )

        if color:
            query = query.where(WordEntryDB.color == color)
        if level:
            query = query.where(WordEntryDB.level == level)

        # 使用复合索引优化查询
        query = query.order_by(
            WordEntryDB.color,
            WordEntryDB.level,
            WordEntryDB.created_at.desc()
        )
        query = query.limit(limit).offset(offset)

        result = await self.session.execute(query)
        return result.scalars().all()

# 批量操作优化
class BatchOperationService:
    async def batch_update_bindings(
        self,
        operations: List[BindingOperation]
    ) -> BatchResult:
        async with self.session.begin():
            # 批量执行操作，减少数据库往返
            for operation in operations:
                if operation.type == "create":
                    await self.create_binding(operation.data)
                elif operation.type == "update":
                    await self.update_binding(operation.id, operation.data)
                elif operation.type == "delete":
                    await self.delete_binding(operation.id)

        # 批量清除缓存
        await self.cache.invalidate_pattern("bindings:*")

        return BatchResult(success=True, processed=len(operations))
```

### 9.3 缓存优化策略

```typescript
// 前端多级缓存
class CacheManager {
  private memoryCache = new Map<string, any>();
  private indexedDB: IDBDatabase;

  async get(key: string): Promise<any> {
    // L1: 内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }

    // L2: IndexedDB缓存
    const cached = await this.getFromIndexedDB(key);
    if (cached) {
      this.memoryCache.set(key, cached);
      return cached;
    }

    return null;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    // 同时更新两级缓存
    this.memoryCache.set(key, value);
    await this.setToIndexedDB(key, value, ttl);
  }
}
```

## 10. 实施计划

### 10.1 开发阶段规划

#### 阶段1：核心数据结构和状态管理 (1-2周)

- **任务1.1**: 创建TypeScript类型定义
  - 定义WordEntry、WordLibraryData、CellWordBinding等核心接口
  - 创建枚举类型BasicColorType、ColorLevel
  - 设置验证规则和约束条件

- **任务1.2**: 实现WordLibraryStore
  - 基于Zustand创建词库状态管理
  - 实现持久化中间件配置
  - 添加基础的CRUD操作方法

- **任务1.3**: 集成到现有MatrixStore
  - 分析现有MatrixStore结构
  - 设计WordLibraryStore与MatrixStore的交互接口
  - 确保状态同步和数据一致性

- **任务1.4**: 基础词库CRUD操作
  - 实现addWord、updateWord、removeWord方法
  - 添加中文字符验证逻辑
  - 实现词库分类和过滤功能

#### 阶段2：词库管理界面 (1-2周)

- **任务2.1**: WordLibraryPanel组件
  - 设计词库管理面板UI
  - 实现分类标签页切换
  - 添加搜索和过滤功能

- **任务2.2**: 词语添加/编辑/删除界面
  - 创建词语表单组件
  - 实现实时验证和错误提示
  - 添加批量操作功能

- **任务2.3**: 按颜色和等级分类显示
  - 实现分类树形结构
  - 添加词语统计信息显示
  - 优化大量词语的渲染性能

- **任务2.4**: 中文字符验证
  - 实现正则表达式验证
  - 添加词语重复检查
  - 集成第三方中文词典API（可选）

#### 阶段3：填词功能实现 (2-3周)

- **任务3.1**: 双击激活填词模式
  - 修改GridCell组件添加双击事件
  - 实现填词模式的视觉状态切换
  - 添加单元格颜色和等级检测

- **任务3.2**: 键盘导航系统
  - 实现KeyboardNavigationService
  - 添加方向键导航逻辑
  - 实现循环导航和边界处理

- **任务3.3**: 词语预览和确认
  - 创建WordSelectionOverlay组件
  - 实现预览模式的临时显示
  - 添加确认和取消操作

- **任务3.4**: 与GridCell组件集成
  - 修改GridCell支持词语显示
  - 实现词语绑定的视觉反馈
  - 添加悬停提示和交互效果

#### 阶段4：查重和版本管理 (1-2周)

- **任务4.1**: 重复检测算法
  - 实现DuplicateDetectionService
  - 开发高效的重复检测算法
  - 添加重复统计和分析功能

- **任务4.2**: 视觉高亮显示
  - 创建DuplicateHighlight组件
  - 实现重复词语的视觉标识
  - 添加高亮开关和自定义样式

- **任务4.3**: 版本保存和加载
  - 实现版本数据的序列化
  - 添加版本元数据计算
  - 实现版本切换和恢复功能

- **任务4.4**: 版本管理界面
  - 创建WordVersionPanel组件
  - 实现版本列表和操作界面
  - 添加版本比较和差异显示

#### 阶段5：后端API开发 (2-3周)

- **任务5.1**: FastAPI路由和模型
  - 设计RESTful API端点
  - 创建Pydantic数据模型
  - 实现请求验证和错误处理

- **任务5.2**: 数据库设计和迁移
  - 设计SQLModel数据库模型
  - 创建数据库迁移脚本
  - 设置索引和约束优化

- **任务5.3**: API端点实现
  - 实现词库管理API
  - 开发绑定关系API
  - 添加版本管理API

- **任务5.4**: 数据验证和错误处理
  - 实现服务端数据验证
  - 添加统一错误处理机制
  - 集成日志记录和监控

#### 阶段6：前后端集成和优化 (1-2周)

- **任务6.1**: API集成和数据同步
  - 集成前端API调用
  - 实现数据同步机制
  - 添加离线模式支持

- **任务6.2**: 性能优化
  - 实现前端虚拟化渲染
  - 优化数据库查询性能
  - 添加缓存层和批量操作

- **任务6.3**: 错误处理和用户体验
  - 完善错误提示和恢复机制
  - 优化加载状态和交互反馈
  - 添加用户操作指南

- **任务6.4**: 测试和文档
  - 编写单元测试和集成测试
  - 完善API文档和用户手册
  - 进行性能测试和优化

### 10.2 里程碑和交付物

#### 里程碑1 (第2周末): 核心架构完成

- **交付物**:
  - 完整的TypeScript类型定义
  - WordLibraryStore基础实现
  - 与现有系统的集成方案

#### 里程碑2 (第4周末): 词库管理功能完成

- **交付物**:
  - 词库管理界面
  - 词语CRUD操作
  - 中文验证和分类功能

#### 里程碑3 (第7周末): 填词功能完成

- **交付物**:
  - 双击激活填词模式
  - 键盘导航系统
  - 词语预览和确认功能

#### 里程碑4 (第9周末): 查重和版本管理完成

- **交付物**:
  - 重复检测和高亮显示
  - 版本保存和加载功能
  - 版本管理界面

#### 里程碑5 (第12周末): 后端API完成

- **交付物**:
  - 完整的后端API
  - 数据库设计和迁移
  - API文档和测试

#### 里程碑6 (第14周末): 系统集成完成

- **交付物**:
  - 前后端完整集成
  - 性能优化和错误处理
  - 测试和文档

### 10.3 风险评估和应对策略

#### 高风险项

1. **与现有MatrixStore的集成复杂度**
   - 风险: 状态管理冲突，数据同步问题
   - 应对: 提前进行详细的架构分析，设计清晰的接口边界

2. **33x33矩阵的性能问题**
   - 风险: 大量单元格导致渲染性能下降
   - 应对: 实现虚拟化渲染，优化重复检测算法

3. **中文字符处理的复杂性**
   - 风险: 字符编码、输入法兼容性问题
   - 应对: 充分测试各种中文输入场景，建立完善的验证机制

#### 中风险项

1. **实时同步的网络延迟**
   - 风险: 网络不稳定导致数据同步失败
   - 应对: 实现离线模式和冲突解决机制

2. **版本管理的数据完整性**
   - 风险: 版本数据损坏或不一致
   - 应对: 实现数据校验和备份机制

### 10.4 技术债务管理

#### 代码质量保证

- 使用TypeScript严格模式
- 实施代码审查流程
- 维护单元测试覆盖率 > 80%
- 定期进行代码重构

#### 文档维护

- 保持API文档与代码同步
- 维护架构决策记录(ADR)
- 定期更新用户手册
- 建立知识库和FAQ

#### 性能监控

- 设置性能基准测试
- 监控关键指标(响应时间、内存使用)
- 定期进行性能优化
- 建立性能回归测试

---

## 📝 总结

本开发指南提供了词库系统的完整技术方案，包括：

1. **完整的数据结构设计** - 涵盖词语、绑定、版本、重复检测等所有核心数据模型
2. **前后端架构方案** - 基于现有技术栈的分层架构设计
3. **详细的API设计** - RESTful API端点和数据模型规范
4. **可视化架构图** - 系统整体架构和业务流程时序图
5. **性能优化策略** - 前端虚拟化、后端缓存、数据库优化等方案
6. **详细实施计划** - 6个阶段、14周的完整开发路线图

该方案确保了系统的高性能、高可用性和良好的用户体验，为后续开发提供了坚实的技术基础。

---

**文档状态**: ✅ 已完成
**下一步行动**: 开始阶段1的开发工作 - 核心数据结构和状态管理
